# 电商退货功能开发 PRP (Pull Request Proposal)

## 项目信息
- **项目名称**: jdl-oms-express
- **功能名称**: 电商退货主产品支持
- **需求文档**: 电商特惠prd.md
- **创建时间**: 2025-09-15
- **开发周期**: 3-5个工作日

## 需求概述
为OMS系统添加【电商退货】主产品支持，包括产品枚举定义、地址修改限制、支付方式限制和消息字段扩展等功能。

## 功能需求分解

### 1. 新主产品支持与数据同步
**需求描述**: 支持主产品传入【电商退货】ed-m-0076，主产品数据同步：外单wbs31位新增枚举；waybillsign 31=【N】

**技术实现**:
- 在ProductEnum中添加新产品类型
- 在WaybillInfoMappingUtil中添加产品标识映射逻辑
- 确保新产品在各个数据流转环节的正确识别

### 2. 地址修改限制规则
**需求描述**: 
- 识别收货信息中的一、二、三级地址编码&名称
- 当用户业务身份为快递C2C时，主产品=【电商退货】时，揽收前不支持修改收货地址，揽收后允许修改

**技术实现**:
- 基于订单状态和产品类型的修改权限控制
- 使用ChangedPropertyDelegate进行字段变更检测

### 3. E卡支付限制
**需求描述**: 【电商退货】主产品写pos台账时不支持e卡支付

**技术实现**:
- 在ECardDisableReasonEnum中添加支付限制原因
- 在台账写入过程中对waybillsign的第一位赋值为0

### 4. 消息字段扩展
**需求描述**: 订单接单成功结果消息EXPRESS_ORDER_CREATE_NOTICE，增加attachmentInfos附加字段

**技术实现**:
- 在消息类DTO中添加新字段
- 在消息转换器中处理新字段的映射

## 详细技术方案

### 1. 产品枚举扩展

#### 1.1 修改文件
- `jdl-oms-express-domain-spec/src/main/java/cn/jdl/oms/express/domain/spec/dict/ProductEnum.java`

#### 1.2 代码变更
```java
// 添加新产品枚举
ECOMMERCE_RETURN("ed-m-0076", "电商退货", 31, "N")
```

#### 1.3 外单映射配置
- `jdl-oms-express-domain-infrastructure/src/main/java/cn/jdl/oms/express/domain/infrs/ohs/locals/message/pl/waybill/WaybillInfoMappingUtil.java`

```java
// 在映射逻辑中添加
if ("N".equals(String.valueOf(markUtil.charAt(31)))) {
    mainProduct.setProductName(ProductEnum.ECOMMERCE_RETURN.getDesc());
    mainProduct.setProductNo(ProductEnum.ECOMMERCE_RETURN.getCode());
}
```

### 2. 地址修改限制实现

#### 2.1 修改文件
- `jdl-oms-express-horz-extension/src/main/java/cn/jdl/oms/express/horz/ext/white/ModifyWhiteExtension.java`

#### 2.2 业务规则实现
```java
private void validateAddressModification(ExpressOrderModel orderModel, ChangedPropertyDelegate changedPropertyDelegate) {
    // 检查是否为电商退货产品
    if (!ProductEnum.ECOMMERCE_RETURN.getCode().equals(orderModel.getProductDelegate().getMajorProductNo())) {
        return;
    }
    
    // 检查业务身份是否为快递C2C
    if (!BusinessIdentityEnum.EXPRESS_C2C.getCode().equals(orderModel.getBusinessIdentity())) {
        return;
    }
    
    // 检查是否为地址字段变更
    if (changedPropertyDelegate.provinceCodeHaveChange() || 
        changedPropertyDelegate.cityCodeHaveChange() || 
        changedPropertyDelegate.countyCodeHaveChange()) {
        
        // 检查订单状态（揽收前/后）
        if (OrderStatusEnum.BEFORE_PICKUP.getCode().equals(orderModel.getOrderSnapshot().getOrderStatus())) {
            throw new BusinessDomainException(UnifiedErrorSpec.Category.BUSINESS_ERROR)
                .withCustom("电商退货产品在揽收前不允许修改收货地址");
        }
    }
}
```

### 3. E卡支付限制实现

#### 3.1 修改文件
- `jdl-oms-express-shared-common/src/main/java/cn/jdl/oms/express/shared/common/dict/ECardDisableReasonEnum.java`

#### 3.2 添加枚举值
```java
ECOMMERCE_RETURN_PAYMENT_DISABLE("ECOMMERCE_RETURN_DISABLE", "电商退货产品不支持E卡支付")
```

#### 3.3 支付校验逻辑
- 在各个OrderBankFacadeTranslator类中添加校验

```java
private ECardDisableReasonEnum checkEcommerceReturnEcardSupport(ExpressOrderModel orderModel) {
    if (ProductEnum.ECOMMERCE_RETURN.getCode().equals(orderModel.getProductDelegate().getMajorProductNo())) {
        // 设置waybillsign第一位为0
        orderModel.getWaybillInfo().setWaybillSign(
            "0" + orderModel.getWaybillInfo().getWaybillSign().substring(1)
        );
        return ECardDisableReasonEnum.ECOMMERCE_RETURN_PAYMENT_DISABLE;
    }
    return null;
}
```

### 4. 消息字段扩展

#### 4.1 查找消息类
通过MQ消息对应关系表，找到EXPRESS_ORDER_CREATE_NOTICE对应的消息类

#### 4.2 消息类扩展
```java
// 在消息DTO中添加字段
private List<AttachmentInfo> attachmentInfos;

// AttachmentInfo定义
public class AttachmentInfo {
    private String attachmentType;
    private String attachmentUrl;
    private String attachmentName;
    // getter/setter
}
```

#### 4.3 转换器更新
在相应的Translator类中添加字段转换逻辑

```java
// 在消息转换器中
if (source.getAttachmentInfos() != null) {
    List<AttachmentInfo> attachmentInfos = source.getAttachmentInfos().stream()
        .map(attachment -> {
            AttachmentInfo info = new AttachmentInfo();
            info.setAttachmentType(attachment.getType());
            info.setAttachmentUrl(attachment.getUrl());
            info.setAttachmentName(attachment.getName());
            return info;
        })
        .collect(Collectors.toList());
    target.setAttachmentInfos(attachmentInfos);
}
```

## 测试用例

### 1. 产品枚举测试
- 测试新产品枚举的正确性
- 测试外单映射的准确性

### 2. 地址修改限制测试
- 测试C2C业务身份下电商退货产品的地址修改限制
- 测试揽收前后的权限控制
- 测试非电商退货产品不受影响

### 3. E卡支付限制测试
- 测试电商退货产品不支持E卡支付
- 测试waybillsign第一位正确设置为0
- 测试其他产品不受影响

### 4. 消息字段测试
- 测试attachmentInfos字段的正确传递
- 测试空值处理
- 测试向下兼容性

## 部署计划

### 阶段1: 产品枚举和外单映射
- 部署ProductEnum更新
- 部署WaybillInfoMappingUtil更新

### 阶段2: 地址修改限制
- 部署ModifyWhiteExtension更新
- 验证业务规则

### 阶段3: E卡支付限制
- 部署ECardDisableReasonEnum更新
- 部署相关Translator更新

### 阶段4: 消息字段扩展
- 部署消息类更新
- 部署转换器更新
- 验证消息发送

## 风险评估

### 低风险项
- 产品枚举扩展：影响范围小，易于回滚
- 消息字段扩展：向下兼容，不影响现有功能

### 中风险项
- 地址修改限制：涉及业务规则，需要充分测试
- E卡支付限制：影响支付流程，需要验证

### 缓解措施
- 灰度发布
- 充分的功能测试和回归测试
- 监控关键指标
- 快速回滚机制

## 相关文件清单

### 核心文件
1. `jdl-oms-express-domain-spec/src/main/java/cn/jdl/oms/express/domain/spec/dict/ProductEnum.java`
2. `jdl-oms-express-domain-infrastructure/src/main/java/cn/jdl/oms/express/domain/infrs/ohs/locals/message/pl/waybill/WaybillInfoMappingUtil.java`
3. `jdl-oms-express-horz-extension/src/main/java/cn/jdl/oms/express/horz/ext/white/ModifyWhiteExtension.java`
4. `jdl-oms-express-shared-common/src/main/java/cn/jdl/oms/express/shared/common/dict/ECardDisableReasonEnum.java`

### 消息相关文件
- 消息类DTO文件（根据MQ消息对应关系表确定）
- 消息转换器Translator类

### 测试文件
- 单元测试文件
- 集成测试文件

## 验收标准

### 功能验收
- [ ] 电商退货产品正确识别
- [ ] 地址修改限制按规则执行
- [ ] E卡支付限制生效
- [ ] 消息字段正确传递

### 技术验收
- [ ] 所有单元测试通过
- [ ] 集成测试通过
- [ ] 代码覆盖率>80%
- [ ] 性能测试通过

### 业务验收
- [ ] 业务方验收通过
- [ ] 产品验收通过
- [ ] 测试环境验证通过
- [ ] 生产环境验证通过